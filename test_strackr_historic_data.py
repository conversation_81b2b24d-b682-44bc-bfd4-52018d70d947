#!/usr/bin/env python3
"""
Strackr Historic Data Pull Script

This script pulls ALL historic Strackr transactions and pushes them to Supabase.
It modifies the normal DAG behavior to fetch all available data instead of just yesterday's data.

This script:
1. Fetches ALL historic transactions from Strackr API (from earliest available date)
2. Transforms them to normalized schema
3. Uploads them to Supabase normalized_transactions table
4. Verifies the data was uploaded correctly

Run this to populate Supabase with complete historic Strackr data.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

# Add dependencies to path
sys.path.append(os.path.join(os.path.dirname(__file__), "dags", "dependencies"))

from strackr.strackr_client import StrackrClient
from strackr.models import transform_strackr_data
from supabase import create_client, Client


def get_historic_date_range():
    """Get a wide historic date range to capture all available data."""
    # Start from a very early date to capture all historic data
    # Strackr typically has data going back 1-2 years
    start_date = "2023-01-01"  # Start from early 2023
    end_date = datetime.now().strftime("%Y-%m-%d")  # Up to today

    print(f"📅 Historic date range: {start_date} to {end_date}")
    return start_date, end_date


def fetch_all_historic_transactions():
    """Fetch all historic transactions from Strackr."""
    print("\n🔍 === FETCHING ALL HISTORIC STRACKR TRANSACTIONS ===")

    try:
        # Initialize Strackr client
        client = StrackrClient()

        # Get historic date range
        start_date, end_date = get_historic_date_range()

        # Fetch all transactions
        print(f"🚀 Starting fetch for date range: {start_date} to {end_date}")
        transactions = client.get_transactions(start_date, end_date)

        print(f"✅ Successfully fetched {len(transactions)} transactions")

        # Log sample raw transaction to debug commission field names
        if transactions:
            print("\n🔍 === SAMPLE RAW TRANSACTION FOR DEBUGGING ===")
            sample_tx = transactions[0]
            print("Available fields in raw transaction:")
            for key, value in sample_tx.items():
                if isinstance(value, (str, int, float)) and len(str(value)) < 100:
                    print(f"   • {key}: {value}")
                else:
                    print(f"   • {key}: {type(value).__name__}")

            # Look specifically for commission-related fields
            commission_fields = [
                k for k in sample_tx.keys() if "commission" in k.lower()
            ]
            if commission_fields:
                print(f"\n💰 Commission-related fields found: {commission_fields}")
                for field in commission_fields:
                    print(f"   • {field}: {sample_tx[field]}")
            else:
                print("\n⚠️ No commission-related fields found in raw data!")

        return transactions

    except Exception as e:
        print(f"❌ Error fetching transactions: {str(e)}")
        import traceback

        traceback.print_exc()
        return []


def transform_all_transactions(raw_transactions):
    """Transform all raw transactions to normalized format."""
    print(f"\n🔄 === TRANSFORMING {len(raw_transactions)} TRANSACTIONS ===")

    transformed_transactions = []
    failed_transformations = 0

    for i, raw_tx in enumerate(raw_transactions):
        try:
            # Transform using the existing model
            transformed_tx = transform_strackr_data(raw_tx)

            if transformed_tx:
                transformed_transactions.append(transformed_tx.to_dict())
            else:
                failed_transformations += 1

            # Progress update every 1000 transactions
            if (i + 1) % 1000 == 0:
                print(f"   📊 Processed {i + 1}/{len(raw_transactions)} transactions")

        except Exception as e:
            failed_transformations += 1
            print(f"   ⚠️ Failed to transform transaction {i}: {str(e)}")

    print(f"✅ Successfully transformed {len(transformed_transactions)} transactions")
    print(f"❌ Failed to transform {failed_transformations} transactions")

    return transformed_transactions


def upload_all_to_supabase(transformed_transactions):
    """Upload all transformed transactions to Supabase."""
    print(
        f"\n📤 === UPLOADING {len(transformed_transactions)} TRANSACTIONS TO SUPABASE ==="
    )

    try:
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

        if not supabase_url or not supabase_key:
            print("❌ Supabase credentials not found in environment")
            return False

        supabase: Client = create_client(supabase_url, supabase_key)
        print("✅ Supabase client initialized")

        # Upload in batches to avoid overwhelming the database
        batch_size = 1000
        total_uploaded = 0

        for i in range(0, len(transformed_transactions), batch_size):
            batch = transformed_transactions[i : i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (
                len(transformed_transactions) + batch_size - 1
            ) // batch_size

            print(
                f"   📦 Uploading batch {batch_num}/{total_batches} ({len(batch)} transactions)"
            )

            try:
                # Use upsert to handle duplicates
                result = (
                    supabase.table("normalized_transactions").upsert(batch).execute()
                )

                if result.data:
                    total_uploaded += len(batch)
                    print(f"   ✅ Batch {batch_num} uploaded successfully")
                else:
                    print(f"   ⚠️ Batch {batch_num} upload returned no data")

            except Exception as e:
                print(f"   ❌ Failed to upload batch {batch_num}: {str(e)}")

        print(f"✅ Upload complete! Total uploaded: {total_uploaded} transactions")
        return total_uploaded > 0

    except Exception as e:
        print(f"❌ Error uploading to Supabase: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


def verify_supabase_data():
    """Verify the data was uploaded correctly to Supabase."""
    print("\n🔍 === VERIFYING SUPABASE DATA ===")

    try:
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        supabase: Client = create_client(supabase_url, supabase_key)

        # Get count and date range of uploaded data
        result = (
            supabase.table("normalized_transactions")
            .select("*", count="exact")
            .eq("platform", "strackr")
            .execute()
        )

        total_count = result.count
        print(f"📊 Total Strackr transactions in database: {total_count}")

        if total_count > 0:
            # Get date range
            date_result = supabase.rpc("get_strackr_date_range").execute()
            if date_result.data:
                print(f"📅 Date range: {date_result.data}")

            # Get sample transactions
            sample_result = (
                supabase.table("normalized_transactions")
                .select(
                    "transaction_id, transaction_date, order_amount, commission_amount, network_name, merchant_name, status"
                )
                .eq("platform", "strackr")
                .order("transaction_date", desc=True)
                .limit(5)
                .execute()
            )

            if sample_result.data:
                print("\n📋 Sample transactions:")
                for tx in sample_result.data:
                    print(
                        f"   • {tx['transaction_id']}: ${tx['order_amount']} from {tx['merchant_name']} ({tx['transaction_date'][:10]})"
                    )

        return total_count

    except Exception as e:
        print(f"❌ Error verifying data: {str(e)}")
        return 0


def run_historic_data_pull():
    """Run the complete historic data pull process."""
    print("🚀 STARTING STRACKR HISTORIC DATA PULL")
    print("=" * 70)

    start_time = time.time()

    try:
        # Step 1: Fetch all historic transactions
        raw_transactions = fetch_all_historic_transactions()
        if not raw_transactions:
            print("❌ No transactions fetched. Exiting.")
            return

        # Step 2: Transform transactions
        transformed_transactions = transform_all_transactions(raw_transactions)
        if not transformed_transactions:
            print("❌ No transactions transformed. Exiting.")
            return

        # Step 3: Upload to Supabase
        upload_success = upload_all_to_supabase(transformed_transactions)
        if not upload_success:
            print("❌ Upload failed. Exiting.")
            return

        # Step 4: Verify data
        final_count = verify_supabase_data()

        # Summary
        total_time = time.time() - start_time
        print(f"\n🎉 === HISTORIC DATA PULL COMPLETE ===")
        print(f"⏱️  Total execution time: {total_time:.2f} seconds")
        print(f"📊 Raw transactions fetched: {len(raw_transactions)}")
        print(f"🔄 Transactions transformed: {len(transformed_transactions)}")
        print(f"📤 Final count in database: {final_count}")

        if final_count > 0:
            print("✅ SUCCESS: Historic Strackr data has been loaded!")
        else:
            print("❌ FAILED: No data found in database after upload")

    except Exception as e:
        print(f"\n❌ HISTORIC DATA PULL FAILED: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    run_historic_data_pull()
