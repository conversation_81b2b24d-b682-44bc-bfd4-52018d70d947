"""
Apple API Data Schemas

This module defines data models and Supabase table schemas for Apple App Store Connect
analytics data. These schemas ensure consistent data structure across the pipeline.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum


class AppleReportType(Enum):
    """Enum for Apple report types."""
    ACTIVE_USERS = "active_users"
    INSTALLS = "installs"


@dataclass
class AppleReportData:
    """
    Raw Apple report data container.
    
    This class holds the raw report data from Apple API before transformation.
    """
    report_id: str
    report_type: AppleReportType
    report_date: str
    raw_data: str
    file_size: int
    created_at: datetime
    
    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "report_id": self.report_id,
            "report_type": self.report_type.value,
            "report_date": self.report_date,
            "raw_data": self.raw_data,
            "file_size": self.file_size,
            "created_at": self.created_at.isoformat()
        }


@dataclass
class AppleActiveUsersMetric:
    """
    Structured data model for Apple active users metrics.
    
    This represents processed active users data suitable for database storage.
    """
    report_date: str
    active_users: int
    sessions: int
    avg_session_duration: float
    device_type: Optional[str] = None
    app_version: Optional[str] = None
    country: Optional[str] = None
    created_at: Optional[datetime] = None
    
    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "report_date": self.report_date,
            "active_users": self.active_users,
            "sessions": self.sessions,
            "avg_session_duration": self.avg_session_duration,
            "device_type": self.device_type,
            "app_version": self.app_version,
            "country": self.country,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


@dataclass
class AppleInstallsMetric:
    """
    Structured data model for Apple installs metrics.
    
    This represents processed installs data suitable for database storage.
    """
    report_date: str
    total_downloads: int
    first_time_downloads: int
    redownloads: int
    updates: int
    country: Optional[str] = None
    source: Optional[str] = None  # App Store, TestFlight, etc.
    created_at: Optional[datetime] = None
    
    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "report_date": self.report_date,
            "total_downloads": self.total_downloads,
            "first_time_downloads": self.first_time_downloads,
            "redownloads": self.redownloads,
            "updates": self.updates,
            "country": self.country,
            "source": self.source,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


# Supabase Table Schemas
# These define the structure of tables in Supabase database

APPLE_REPORTS_TABLE_SCHEMA = """
-- Table for storing raw Apple report data
CREATE TABLE IF NOT EXISTS apple_analytics_reports (
    id SERIAL PRIMARY KEY,
    report_id VARCHAR(255) UNIQUE NOT NULL,
    report_type VARCHAR(50) NOT NULL,
    report_date DATE NOT NULL,
    raw_data TEXT,
    file_size INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_apple_reports_date ON apple_analytics_reports(report_date);
CREATE INDEX IF NOT EXISTS idx_apple_reports_type ON apple_analytics_reports(report_type);
CREATE INDEX IF NOT EXISTS idx_apple_reports_created ON apple_analytics_reports(created_at);
"""

APPLE_ACTIVE_USERS_TABLE_SCHEMA = """
-- Table for storing processed active users metrics
CREATE TABLE IF NOT EXISTS apple_active_users_metrics (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    active_users INTEGER NOT NULL DEFAULT 0,
    sessions INTEGER NOT NULL DEFAULT 0,
    avg_session_duration DECIMAL(10,2) DEFAULT 0.00,
    device_type VARCHAR(50),
    app_version VARCHAR(20),
    country VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique records per date/device/country combination
    UNIQUE(report_date, device_type, app_version, country)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_apple_active_users_date ON apple_active_users_metrics(report_date);
CREATE INDEX IF NOT EXISTS idx_apple_active_users_country ON apple_active_users_metrics(country);
CREATE INDEX IF NOT EXISTS idx_apple_active_users_device ON apple_active_users_metrics(device_type);
"""

APPLE_INSTALLS_TABLE_SCHEMA = """
-- Table for storing processed installs metrics
CREATE TABLE IF NOT EXISTS apple_installs_metrics (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    total_downloads INTEGER NOT NULL DEFAULT 0,
    first_time_downloads INTEGER NOT NULL DEFAULT 0,
    redownloads INTEGER NOT NULL DEFAULT 0,
    updates INTEGER NOT NULL DEFAULT 0,
    country VARCHAR(10),
    source VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique records per date/country/source combination
    UNIQUE(report_date, country, source)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_apple_installs_date ON apple_installs_metrics(report_date);
CREATE INDEX IF NOT EXISTS idx_apple_installs_country ON apple_installs_metrics(country);
CREATE INDEX IF NOT EXISTS idx_apple_installs_source ON apple_installs_metrics(source);
"""

# Combined schema for easy deployment
COMPLETE_APPLE_SCHEMA = f"""
-- Apple Analytics Database Schema
-- Generated on {datetime.now().isoformat()}

{APPLE_REPORTS_TABLE_SCHEMA}

{APPLE_ACTIVE_USERS_TABLE_SCHEMA}

{APPLE_INSTALLS_TABLE_SCHEMA}

-- Views for easier data analysis
CREATE OR REPLACE VIEW apple_daily_summary AS
SELECT 
    au.report_date,
    au.active_users,
    au.sessions,
    au.avg_session_duration,
    im.total_downloads,
    im.first_time_downloads,
    im.redownloads,
    im.updates
FROM apple_active_users_metrics au
FULL OUTER JOIN apple_installs_metrics im ON au.report_date = im.report_date
ORDER BY au.report_date DESC;

-- Function to get latest metrics
CREATE OR REPLACE FUNCTION get_latest_apple_metrics()
RETURNS TABLE (
    report_date DATE,
    active_users INTEGER,
    total_downloads INTEGER,
    first_time_downloads INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(au.report_date, im.report_date) as report_date,
        COALESCE(SUM(au.active_users), 0)::INTEGER as active_users,
        COALESCE(SUM(im.total_downloads), 0)::INTEGER as total_downloads,
        COALESCE(SUM(im.first_time_downloads), 0)::INTEGER as first_time_downloads
    FROM apple_active_users_metrics au
    FULL OUTER JOIN apple_installs_metrics im ON au.report_date = im.report_date
    WHERE COALESCE(au.report_date, im.report_date) >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY COALESCE(au.report_date, im.report_date)
    ORDER BY report_date DESC;
END;
$$ LANGUAGE plpgsql;
"""


def get_table_schemas() -> Dict[str, str]:
    """
    Get all table schemas as a dictionary.
    
    Returns:
        Dict[str, str]: Dictionary mapping table names to their schemas
    """
    return {
        "apple_analytics_reports": APPLE_REPORTS_TABLE_SCHEMA,
        "apple_active_users_metrics": APPLE_ACTIVE_USERS_TABLE_SCHEMA,
        "apple_installs_metrics": APPLE_INSTALLS_TABLE_SCHEMA,
        "complete_schema": COMPLETE_APPLE_SCHEMA
    }


def validate_active_users_data(data: Dict[str, Any]) -> bool:
    """
    Validate active users data structure.
    
    Args:
        data (Dict[str, Any]): Data to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    required_fields = ["report_date", "active_users", "sessions", "avg_session_duration"]
    
    for field in required_fields:
        if field not in data:
            return False
    
    # Validate data types
    try:
        int(data["active_users"])
        int(data["sessions"])
        float(data["avg_session_duration"])
        # Validate date format
        datetime.strptime(data["report_date"], "%Y-%m-%d")
        return True
    except (ValueError, TypeError):
        return False


def validate_installs_data(data: Dict[str, Any]) -> bool:
    """
    Validate installs data structure.
    
    Args:
        data (Dict[str, Any]): Data to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    required_fields = ["report_date", "total_downloads", "first_time_downloads", "redownloads", "updates"]
    
    for field in required_fields:
        if field not in data:
            return False
    
    # Validate data types
    try:
        int(data["total_downloads"])
        int(data["first_time_downloads"])
        int(data["redownloads"])
        int(data["updates"])
        # Validate date format
        datetime.strptime(data["report_date"], "%Y-%m-%d")
        return True
    except (ValueError, TypeError):
        return False
