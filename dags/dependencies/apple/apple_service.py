"""
Apple API Service

This service handles interactions with Apple App Store Connect API to:
1. Request analytics reports for active users and app installs
2. Poll for report completion status
3. Download completed reports

The service manages JWT token authentication and implements proper retry logic
for API calls with rate limiting considerations.
"""

import requests
import time
import os
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timed<PERSON>ta
from enum import Enum

from .jwt_service import AppleJWTService

logger = logging.getLogger(__name__)


class ReportType(Enum):
    """Enum for Apple Analytics report types."""
    APP_USAGE = "appUsage"  # Active users report
    APP_STORE_DOWNLOADS = "appStoreDownloads"  # Installs report


class ReportStatus(Enum):
    """Enum for report request status."""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS" 
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class AppleAPIService:
    """Service for interacting with Apple App Store Connect Analytics API."""
    
    BASE_URL = "https://api.appstoreconnect.apple.com/v1"
    
    def __init__(self, app_id: str = None):
        """
        Initialize the Apple API service.
        
        Args:
            app_id (str, optional): Apple App ID. If not provided, will use APPLE_APP_ID env var.
        """
        self.jwt_service = AppleJWTService()
        self.app_id = app_id or os.getenv("APPLE_APP_ID")
        self.session = requests.Session()
        self._current_token = None
        self._token_expiry = None
        
        if not self.app_id:
            raise ValueError("Apple App ID is required. Set APPLE_APP_ID environment variable or pass app_id parameter.")
    
    def _get_valid_token(self) -> str:
        """
        Get a valid JWT token, generating a new one if needed.
        
        Returns:
            str: Valid JWT token
            
        Raises:
            Exception: If token generation fails
        """
        # Check if current token is still valid
        if (self._current_token and 
            self._token_expiry and 
            datetime.now() < self._token_expiry - timedelta(minutes=2)):  # 2-minute buffer
            return self._current_token
        
        # Generate new token
        logger.info("Generating new JWT token")
        self._current_token = self.jwt_service.generate_jwt_token()
        self._token_expiry = self.jwt_service.get_token_expiry(self._current_token)
        
        return self._current_token
    
    def _make_api_request(self, method: str, endpoint: str, data: Dict = None, 
                         max_retries: int = 3) -> Dict[str, Any]:
        """
        Make an authenticated API request to Apple App Store Connect.
        
        Args:
            method (str): HTTP method (GET, POST, etc.)
            endpoint (str): API endpoint (without base URL)
            data (Dict, optional): Request payload for POST requests
            max_retries (int): Maximum number of retry attempts
            
        Returns:
            Dict[str, Any]: API response data
            
        Raises:
            requests.RequestException: If request fails after all retries
        """
        url = f"{self.BASE_URL}/{endpoint.lstrip('/')}"
        
        for attempt in range(max_retries + 1):
            try:
                # Get valid token
                token = self._get_valid_token()
                
                # Set headers
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                # Log detailed request information
                logger.info(f"=== API REQUEST DETAILS (Attempt {attempt + 1}) ===")
                logger.info(f"Method: {method.upper()}")
                logger.info(f"URL: {url}")
                logger.info(f"Headers: {headers}")
                if data:
                    logger.info(f"Request Payload: {data}")
                
                # Make request
                if method.upper() == "GET":
                    response = self.session.get(url, headers=headers)
                elif method.upper() == "POST":
                    response = self.session.post(url, headers=headers, json=data)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                # Log detailed response information
                logger.info(f"=== API RESPONSE DETAILS ===")
                logger.info(f"Status Code: {response.status_code}")
                logger.info(f"Response Headers: {dict(response.headers)}")
                logger.info(f"Response Body: {response.text}")
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 60))
                    logger.warning(f"Rate limited. Waiting {retry_after} seconds before retry.")
                    time.sleep(retry_after)
                    continue
                
                # Check for success
                response.raise_for_status()
                
                logger.info(f"Successfully made {method} request to {endpoint}")
                return response.json()
                
            except requests.RequestException as e:
                logger.error(f"API request failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                
                # Log error response details
                if hasattr(e, 'response') and e.response is not None:
                    logger.error(f"Error Response Status: {e.response.status_code}")
                    logger.error(f"Error Response Headers: {dict(e.response.headers)}")
                    logger.error(f"Error Response Body: {e.response.text}")
                
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    raise
    
    def request_analytics_report(self, access_type: str = "ONGOING") -> str:
        """
        Request an analytics report from Apple.
        
        Args:
            access_type (str): Type of access for the report. Options:
                             - "ONGOING": Most common, generates daily data for all frequencies
                             - "ONE_TIME_SNAPSHOT": One-time report for historical data
            
        Returns:
            str: Report request ID for polling status
            
        Raises:
            Exception: If report request fails
        """
        
        payload = {
            "data": {
                "type": "analyticsReportRequests",
                "attributes": {
                    "accessType": access_type
                },
                "relationships": {
                    "app": {
                        "data": {
                            "type": "apps",
                            "id": self.app_id
                        }
                    }
                }
            }
        }
        
        logger.info(f"Requesting analytics report with access type: {access_type}")
        
        try:
            response = self._make_api_request("POST", "/analyticsReportRequests", payload)
            
            request_id = response["data"]["id"]
            logger.info(f"Successfully requested analytics report with access type {access_type}. Request ID: {request_id}")
            
            return request_id
            
        except Exception as e:
            logger.error(f"Failed to request analytics report with access type {access_type}: {e}")
            raise
    
    def get_report_status(self, request_id: str) -> Dict[str, Any]:
        """
        Check the status of a report request.
        
        Args:
            request_id (str): The report request ID
            
        Returns:
            Dict[str, Any]: Report status information including:
                - status: Current status of the report
                - download_url: URL to download report (if completed)
                - error_message: Error details (if failed)
        """
        try:
            response = self._make_api_request("GET", f"/analyticsReportRequests/{request_id}")
            
            attributes = response["data"]["attributes"]
            status = attributes.get("status", "UNKNOWN")
            
            result = {
                "request_id": request_id,
                "status": status,
                "download_url": attributes.get("downloadUrl"),
                "error_message": attributes.get("errorMessage")
            }
            
            logger.info(f"Report {request_id} status: {status}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to get status for report {request_id}: {e}")
            raise
    
    def poll_report_completion(self, request_id: str, max_wait_minutes: int = 30,
                             poll_interval_seconds: int = 60) -> Dict[str, Any]:
        """
        Poll for report completion with exponential backoff.
        
        Args:
            request_id (str): The report request ID
            max_wait_minutes (int): Maximum time to wait for completion
            poll_interval_seconds (int): Initial polling interval
            
        Returns:
            Dict[str, Any]: Final report status
            
        Raises:
            TimeoutError: If report doesn't complete within max_wait_minutes
            Exception: If report generation fails
        """
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        current_interval = poll_interval_seconds
        
        logger.info(f"Polling for completion of report {request_id} (max wait: {max_wait_minutes} minutes)")
        
        while True:
            elapsed_time = time.time() - start_time
            
            if elapsed_time > max_wait_seconds:
                raise TimeoutError(f"Report {request_id} did not complete within {max_wait_minutes} minutes")
            
            try:
                status_info = self.get_report_status(request_id)
                status = status_info["status"]
                
                if status == "COMPLETED":
                    logger.info(f"Report {request_id} completed successfully")
                    return status_info
                elif status == "FAILED":
                    error_msg = status_info.get("error_message", "Unknown error")
                    raise Exception(f"Report {request_id} failed: {error_msg}")
                elif status in ["PENDING", "IN_PROGRESS"]:
                    logger.info(f"Report {request_id} still {status.lower()}. Waiting {current_interval} seconds...")
                    time.sleep(current_interval)
                    
                    # Exponential backoff with max interval of 5 minutes
                    current_interval = min(current_interval * 1.5, 300)
                else:
                    logger.warning(f"Unknown status for report {request_id}: {status}")
                    time.sleep(current_interval)
                    
            except Exception as e:
                logger.error(f"Error while polling report {request_id}: {e}")
                raise
    
    def get_available_reports(self, request_id: str) -> List[Dict[str, Any]]:
        """
        Get all available reports for a report request.
        
        Args:
            request_id (str): The report request ID
            
        Returns:
            List[Dict[str, Any]]: List of available reports with their IDs and metadata
        """
        try:
            response = self._make_api_request("GET", f"/analyticsReportRequests/{request_id}/reports")
            reports = response.get("data", [])
            
            logger.info(f"Found {len(reports)} available reports for request {request_id}")
            return reports
            
        except Exception as e:
            logger.error(f"Failed to get available reports for {request_id}: {e}")
            raise
    
    def get_report_instances(self, report_id: str) -> List[Dict[str, Any]]:
        """
        Get instances (actual data) for a specific report.
        
        Args:
            report_id (str): The specific report ID (e.g., r1-{request_id})
            
        Returns:
            List[Dict[str, Any]]: List of report instances with download URLs
        """
        try:
            response = self._make_api_request("GET", f"/analyticsReports/{report_id}/instances")
            instances = response.get("data", [])
            
            logger.info(f"Found {len(instances)} instances for report {report_id}")
            return instances
            
        except Exception as e:
            logger.error(f"Failed to get instances for report {report_id}: {e}")
            raise
    
    def poll_report_instances(self, request_id: str, target_reports: List[str] = None, 
                            max_wait_minutes: int = 60, poll_interval_seconds: int = 120) -> Dict[str, List[Dict]]:
        """
        Poll for report instances to be populated with data.
        
        Args:
            request_id (str): The analytics report request ID
            target_reports (List[str]): Specific report names to wait for (e.g., ['App Usage Standard', 'App Store Downloads Standard'])
            max_wait_minutes (int): Maximum time to wait
            poll_interval_seconds (int): Polling interval
            
        Returns:
            Dict[str, List[Dict]]: Dictionary mapping report names to their instances
        """
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        current_interval = poll_interval_seconds
        
        if not target_reports:
            target_reports = ['App Usage Standard', 'App Store Downloads Standard']
        
        logger.info(f"Polling for report instances: {target_reports} (max wait: {max_wait_minutes} minutes)")
        
        while True:
            elapsed_time = time.time() - start_time
            
            if elapsed_time > max_wait_seconds:
                raise TimeoutError(f"Report instances did not populate within {max_wait_minutes} minutes")
            
            try:
                # Get all available reports
                available_reports = self.get_available_reports(request_id)
                
                # Find target reports and check their instances
                found_instances = {}
                all_ready = True
                
                for report in available_reports:
                    report_name = report.get('attributes', {}).get('name', '')
                    
                    if report_name in target_reports:
                        report_id = report['id']
                        instances = self.get_report_instances(report_id)
                        
                        if instances:  # Has data
                            found_instances[report_name] = instances
                            logger.info(f"✅ {report_name}: {len(instances)} instances ready")
                        else:  # No data yet
                            all_ready = False
                            logger.info(f"⏳ {report_name}: No instances yet")
                
                if all_ready and len(found_instances) == len(target_reports):
                    logger.info(f"All target reports ready: {list(found_instances.keys())}")
                    return found_instances
                
                logger.info(f"Waiting {current_interval} seconds for more instances...")
                time.sleep(current_interval)
                
                # Exponential backoff with max interval of 10 minutes
                current_interval = min(current_interval * 1.2, 600)
                
            except Exception as e:
                logger.error(f"Error while polling report instances: {e}")
                raise
    
    def download_report_data(self, download_url: str) -> bytes:
        """
        Download report data from the provided URL.
        
        Args:
            download_url (str): URL to download the report
            
        Returns:
            bytes: Raw report data
            
        Raises:
            requests.RequestException: If download fails
        """
        try:
            logger.info(f"Downloading report data from {download_url}")
            
            response = self.session.get(download_url)
            response.raise_for_status()
            
            logger.info(f"Successfully downloaded report data ({len(response.content)} bytes)")
            return response.content
            
        except Exception as e:
            logger.error(f"Failed to download report data: {e}")
            raise
    
    def request_active_users_report(self, access_type: str = "ONGOING") -> str:
        """
        Convenience method to request analytics report (covers all report types).
        Note: Apple's new API generates reports for all metrics in one request.
        
        Args:
            access_type (str): "ONGOING" for current data or "ONE_TIME_SNAPSHOT" for historical
            
        Returns:
            str: Report request ID
        """
        return self.request_analytics_report(access_type)
    
    def request_installs_report(self, access_type: str = "ONGOING") -> str:
        """
        Convenience method to request analytics report (covers all report types).
        Note: Apple's new API generates reports for all metrics in one request.
        
        Args:
            access_type (str): "ONGOING" for current data or "ONE_TIME_SNAPSHOT" for historical
            
        Returns:
            str: Report request ID
        """
        return self.request_analytics_report(access_type)
