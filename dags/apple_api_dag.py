"""
Apple API Analytics Pipeline DAG

This DAG fetches analytics data from Apple App Store Connect API,
transforms it to normalized schemas, and stores the results in Supabase.

Key Reports Collected:
1. Active Users Analytics (appUsage report type)
2. App Store Downloads/Installs (appStoreDownloads report type)

The pipeline follows these steps:
1. Generate JWT token for Apple API authentication
2. Request both analytics reports from Apple
3. Poll for report completion and download data
4. Transform raw data to structured format
5. Upload processed data to Supabase

Schedule: Daily at 4 AM UTC
Catchup: False (only process current data)
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import logging

# Import our custom functions
from dependencies.apple.apple_tasks import (
    generate_apple_jwt_task,
    request_apple_reports_task,
    poll_apple_reports_task,
    transform_apple_data_task,
    upload_apple_data_to_supabase_task,
)
from dependencies.utils.misc import IS_DEV

logger = logging.getLogger(__name__)

# Configuration Constants
PROJECT_ID = "phia-prod-416420"
APPLE_ANALYTICS_TABLE = "apple_analytics_reports"

# DAG Configuration
default_args = {
    "owner": "data-team",
    "depends_on_past": False,
    "start_date": datetime(2024, 1, 1),
    "email_on_failure": True if not IS_DEV() else False,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=10),  # Longer delay for Apple API rate limits
    "email": ["<EMAIL>"],
}

# Create the DAG
dag = DAG(
    "apple_api_analytics_pipeline",
    default_args=default_args,
    description="Fetch and process Apple App Store Connect analytics data",
    schedule_interval="0 4 * * *" if not IS_DEV() else None,  # Daily at 4 AM UTC
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["apple", "analytics", "app-store", "daily"],
    max_active_runs=1,  # Prevent overlapping runs due to long polling times
)

# Task 1: Generate JWT token for Apple API authentication
generate_jwt = PythonOperator(
    task_id="generate_apple_jwt",
    python_callable=generate_apple_jwt_task,
    dag=dag,
    doc_md="""
    ## Generate Apple JWT Token

    This task generates a fresh JWT token for Apple App Store Connect API authentication.

    **What it does:**
    - Loads Apple API credentials from environment variables
    - Generates ES256 JWT token with 20-minute expiry (Apple's maximum)
    - Validates token structure and expiration
    - Stores token information for subsequent tasks

    **Required Environment Variables:**
    - `APPLE_KEY_ID`: Your Key ID from App Store Connect
    - `APPLE_ISSUER_ID`: Your Issuer ID from App Store Connect  
    - `APPLE_PRIVATE_KEY_PATH`: Path to your private key (.p8) file
    - `APPLE_APP_ID`: Your app's Adam ID

    **Output:**
    - JWT token validation status
    - Token expiration timestamp
    - Authentication readiness for API calls
    """,
)

# Task 2: Request analytics reports from Apple API
request_reports = PythonOperator(
    task_id="request_apple_reports",
    python_callable=request_apple_reports_task,
    dag=dag,
    doc_md="""
    ## Request Apple Analytics Reports

    This task initiates the generation of both active users and installs reports.

    **What it does:**
    - Uses JWT token from previous task for authentication
    - Requests active users report (appUsage type) for yesterday
    - Requests installs report (appStoreDownloads type) for yesterday
    - Returns request IDs for polling

    **Reports Requested:**
    1. **Active Users Report**: Daily active users, sessions, session duration
    2. **Installs Report**: Total downloads, first-time downloads, redownloads, updates

    **Output:**
    - Request IDs for both reports
    - Report date (yesterday)
    - Request status confirmation
    """,
)

# Task 3: Poll for report completion and download data
poll_and_download = PythonOperator(
    task_id="poll_apple_reports",
    python_callable=poll_apple_reports_task,
    dag=dag,
    execution_timeout=timedelta(minutes=45),  # Allow time for Apple report generation
    doc_md="""
    ## Poll and Download Apple Reports

    This task polls for report completion and downloads the generated data.

    **What it does:**
    - Polls both report requests until completion (max 30 minutes each)
    - Uses exponential backoff to avoid overwhelming Apple's API
    - Downloads report data once generation is complete
    - Handles rate limiting and temporary failures

    **Polling Strategy:**
    - Initial interval: 60 seconds
    - Exponential backoff with max 5-minute intervals
    - Maximum wait time: 30 minutes per report
    - Automatic retry on transient failures

    **Output:**
    - Raw report data for both report types
    - File sizes and download statistics
    - Report completion timestamps
    """,
)

# Task 4: Transform raw data to structured format
transform_data = PythonOperator(
    task_id="transform_apple_data",
    python_callable=transform_apple_data_task,
    dag=dag,
    doc_md="""
    ## Transform Apple Report Data

    This task processes raw Apple report data into structured format for database storage.

    **What it does:**
    - Parses CSV/JSON data from Apple reports
    - Transforms to normalized database schemas
    - Validates data quality and completeness
    - Handles missing or malformed data gracefully

    **Data Transformations:**
    - **Active Users**: Parse daily metrics, session data, device breakdowns
    - **Installs**: Parse download counts, source attribution, geographic data
    - **Validation**: Ensure data types, ranges, and required fields

    **Output:**
    - Structured active users metrics
    - Structured installs metrics
    - Data quality statistics
    - Transformation success/failure counts
    """,
)

# Task 5: Upload processed data to Supabase
upload_to_supabase = PythonOperator(
    task_id="upload_apple_data_to_supabase",
    python_callable=upload_apple_data_to_supabase_task,
    dag=dag,
    doc_md="""
    ## Upload Apple Data to Supabase

    This task uploads the processed analytics data to Supabase tables.

    **What it does:**
    - Takes structured data from transformation task
    - Uploads to multiple Supabase tables with conflict resolution
    - Handles upserts for existing records (by date)
    - Provides comprehensive upload statistics

    **Tables Updated:**
    - `apple_analytics_reports`: Raw report metadata and storage
    - `apple_active_users_metrics`: Processed active users data
    - `apple_installs_metrics`: Processed installs and downloads data

    **Features:**
    - Batch uploads for performance
    - Automatic conflict resolution (upsert by date)
    - Data validation before upload
    - Detailed error reporting and rollback on failures

    **Output:**
    - Upload success/failure status per table
    - Record counts uploaded
    - Database connection status
    - Error details for any failures
    """,
)

# Define task dependencies
generate_jwt >> request_reports >> poll_and_download >> transform_data >> upload_to_supabase

# Add comprehensive DAG documentation
dag.doc_md = """
# Apple API Analytics Pipeline DAG

This DAG processes analytics data from Apple App Store Connect API for daily reporting and analysis.

## Overview

This pipeline fetches key analytics metrics from Apple's App Store Connect API, transforms them
to normalized Supabase schemas, and stores them for dashboard consumption and business intelligence.

## Key Metrics Collected

### 1. Active Users Analytics (appUsage Report)
- Daily active users count
- Total sessions per day
- Average session duration
- Device type breakdown (iPhone, iPad, etc.)
- App version distribution
- Geographic distribution

### 2. App Store Downloads (appStoreDownloads Report)
- Total downloads per day
- First-time downloads vs redownloads
- App updates count
- Source attribution (App Store, TestFlight)
- Geographic distribution
- Download trends

## Data Flow

1. **Authentication**: Generate JWT token using Apple API credentials (ES256, 20min expiry)
2. **Request**: Initiate report generation for both active users and installs (yesterday's data)
3. **Poll**: Wait for Apple to generate reports (typically 5-15 minutes)
4. **Download**: Retrieve completed report data (CSV/JSON format)
5. **Transform**: Parse and structure data for database storage
6. **Upload**: Store processed data in Supabase with conflict resolution

## Schedule & Timing

- **Frequency**: Daily at 4 AM UTC
- **Data Lag**: Reports for previous day (D-1)
- **Processing Time**: Typically 15-30 minutes
- **Catchup**: Disabled (only processes current data)
- **Retries**: 2 attempts with 10-minute delays

## Environment Requirements

### Required Environment Variables
```
APPLE_KEY_ID=your_key_id_here
APPLE_ISSUER_ID=your_issuer_id_here  
APPLE_PRIVATE_KEY_PATH=/path/to/private_key.p8
APPLE_APP_ID=your_app_adam_id_here
```

### Dependencies
- Apple App Store Connect API access
- Valid .p8 private key file
- Supabase database connection
- PyJWT library for token generation

## Database Schema

### Tables Created/Updated
- **apple_analytics_reports**: Raw report metadata and storage
- **apple_active_users_metrics**: Daily active users, sessions, duration metrics
- **apple_installs_metrics**: Downloads, installs, updates, and source data

### Data Retention
- Raw reports: 90 days (configurable)
- Processed metrics: Indefinite (for historical analysis)
- Automatic cleanup of old raw data

## Monitoring & Alerts

### Success Metrics
- Reports successfully requested and downloaded
- Data transformation completion rate
- Database upload success rate
- Processing time within SLA (< 45 minutes)

### Failure Scenarios
- Apple API authentication failures
- Report generation timeouts (> 30 minutes)
- Data transformation errors
- Database connection/upload failures

### Notifications
- Email alerts <NAME_EMAIL> on failures
- Slack notifications for critical errors (if configured)
- Daily success summary reports

## Troubleshooting

### Common Issues
- **Authentication Failures**: Check Apple API credentials and private key file
- **Report Timeouts**: Apple's report generation can be slow during peak times
- **Rate Limiting**: Built-in exponential backoff handles API rate limits
- **Data Quality**: Transformation handles missing/malformed data gracefully

### Recovery Procedures
- Failed runs can be manually triggered for specific dates
- Raw report data is preserved for reprocessing
- Database transactions ensure data consistency

## Performance Considerations

- **API Rate Limits**: Apple allows 60 requests/hour - we use ~10 per run
- **Report Generation**: Apple reports typically take 5-15 minutes to generate
- **Data Volume**: Reports are typically small (< 1MB) but can vary
- **Database Load**: Batch uploads minimize database impact

## Security

- JWT tokens expire after 20 minutes (Apple's maximum)
- Private keys stored securely outside version control
- Database connections use encrypted channels
- API credentials rotated regularly (recommended)
"""

if __name__ == "__main__":
    dag.test()
