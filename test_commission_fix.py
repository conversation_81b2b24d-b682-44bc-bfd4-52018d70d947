#!/usr/bin/env python3
"""
Test Commission Fix

This script tests the commission field fix by fetching a small sample of recent transactions,
transforming them with the corrected field mapping, and verifying commission amounts are correct.
"""

import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add dependencies to path
sys.path.append(os.path.join(os.path.dirname(__file__), "dags", "dependencies"))

from strackr.strackr_client import StrackrClient
from strackr.models import transform_strackr_data


def test_commission_fix():
    """Test that commission amounts are now correctly extracted."""
    print("🔧 === TESTING COMMISSION FIX ===")
    
    try:
        # Initialize Strackr client
        client = StrackrClient()
        
        # Get recent date range (just last few days)
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")
        
        print(f"📅 Fetching sample data from {start_date} to {end_date}")
        
        # Fetch small sample of transactions
        transactions = client.get_transactions(start_date, end_date)
        
        if not transactions:
            print("❌ No transactions found in recent days")
            return
            
        print(f"✅ Found {len(transactions)} transactions")
        
        # Test transformation on first few transactions
        print(f"\n🔄 === TESTING TRANSFORMATION ===")
        
        successful_transforms = 0
        non_zero_commissions = 0
        
        for i, raw_tx in enumerate(transactions[:10]):  # Test first 10
            print(f"\n--- Transaction {i+1} ---")
            print(f"Raw data: order_amount={raw_tx.get('order_amount')}, revenue={raw_tx.get('revenue')}, rate={raw_tx.get('rate')}")
            
            # Transform the transaction
            transformed = transform_strackr_data(raw_tx)
            
            if transformed:
                successful_transforms += 1
                tx_dict = transformed.to_dict()
                
                print(f"Transformed: order_amount={tx_dict['order_amount']}, commission_amount={tx_dict['commission_amount']}")
                
                if float(tx_dict['commission_amount']) > 0:
                    non_zero_commissions += 1
                    print(f"✅ Non-zero commission found: ${tx_dict['commission_amount']}")
                else:
                    print(f"⚠️ Zero commission (might be pending/declined)")
            else:
                print(f"❌ Transformation failed")
        
        print(f"\n📊 === RESULTS ===")
        print(f"✅ Successful transformations: {successful_transforms}/10")
        print(f"💰 Non-zero commissions: {non_zero_commissions}/10")
        
        if non_zero_commissions > 0:
            print(f"🎉 SUCCESS: Commission fix is working! Found {non_zero_commissions} transactions with commission data.")
        else:
            print(f"⚠️ No non-zero commissions found. This might be normal if all recent transactions are pending/declined.")
            
            # Check status distribution
            statuses = [tx.get('status_id', 'unknown') for tx in transactions[:10]]
            print(f"📊 Status distribution in sample: {set(statuses)}")
        
    except Exception as e:
        print(f"❌ Error testing commission fix: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_commission_fix()
