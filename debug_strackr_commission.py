#!/usr/bin/env python3
"""
Debug Strackr Commission Fields

This script fetches a small sample of Strackr transactions to debug why commission amounts are showing as 0.
It will examine the raw API response structure to identify the correct field names for commission data.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add dependencies to path
sys.path.append(os.path.join(os.path.dirname(__file__), "dags", "dependencies"))

from strackr.strackr_client import StrackrClient


def debug_commission_fields():
    """Debug commission field names in Strackr API response."""
    print("🔍 === DEBUGGING STRACKR COMMISSION FIELDS ===")
    
    try:
        # Initialize Strackr client
        client = StrackrClient()
        
        # Get recent date range (just last few days to get small sample)
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=3)).strftime("%Y-%m-%d")
        
        print(f"📅 Fetching sample data from {start_date} to {end_date}")
        
        # Fetch small sample of transactions
        transactions = client.get_transactions(start_date, end_date)
        
        if not transactions:
            print("❌ No transactions found in recent days")
            return
            
        print(f"✅ Found {len(transactions)} transactions")
        
        # Examine first few transactions
        for i, tx in enumerate(transactions[:3]):
            print(f"\n🔍 === TRANSACTION {i+1} ===")
            print(f"Transaction ID: {tx.get('id', 'unknown')}")
            
            # Print all fields to see structure
            print("\nAll available fields:")
            for key, value in sorted(tx.items()):
                if isinstance(value, (str, int, float)):
                    if len(str(value)) < 100:
                        print(f"   • {key}: {value}")
                    else:
                        print(f"   • {key}: {type(value).__name__} (length: {len(str(value))})")
                elif isinstance(value, dict):
                    print(f"   • {key}: dict with keys: {list(value.keys())}")
                elif isinstance(value, list):
                    print(f"   • {key}: list with {len(value)} items")
                else:
                    print(f"   • {key}: {type(value).__name__}")
            
            # Look for commission-related fields
            commission_fields = []
            for key in tx.keys():
                if any(word in key.lower() for word in ['commission', 'payout', 'earning', 'fee', 'rate']):
                    commission_fields.append(key)
            
            if commission_fields:
                print(f"\n💰 Potential commission fields: {commission_fields}")
                for field in commission_fields:
                    print(f"   • {field}: {tx[field]}")
            else:
                print("\n⚠️ No obvious commission fields found")
            
            # Check if there are nested objects that might contain commission data
            nested_objects = [key for key, value in tx.items() if isinstance(value, dict)]
            if nested_objects:
                print(f"\n📦 Nested objects to check: {nested_objects}")
                for obj_key in nested_objects:
                    obj = tx[obj_key]
                    commission_in_nested = [k for k in obj.keys() if any(word in k.lower() for word in ['commission', 'payout', 'earning', 'fee', 'rate'])]
                    if commission_in_nested:
                        print(f"   • {obj_key} contains: {commission_in_nested}")
                        for field in commission_in_nested:
                            print(f"     - {field}: {obj[field]}")
            
            print("-" * 50)
        
        # Look for transactions with non-zero amounts
        print(f"\n🔍 === LOOKING FOR NON-ZERO COMMISSION TRANSACTIONS ===")
        non_zero_commission = []
        
        for tx in transactions:
            # Check various possible commission field names
            possible_fields = ['commission_amount', 'commission', 'payout', 'earnings', 'fee']
            for field in possible_fields:
                value = tx.get(field, 0)
                try:
                    if float(value) > 0:
                        non_zero_commission.append({
                            'id': tx.get('id'),
                            'field': field,
                            'value': value,
                            'order_amount': tx.get('order_amount', 0)
                        })
                except (ValueError, TypeError):
                    pass
        
        if non_zero_commission:
            print(f"✅ Found {len(non_zero_commission)} transactions with non-zero commission:")
            for tx in non_zero_commission[:5]:  # Show first 5
                print(f"   • ID {tx['id']}: {tx['field']} = {tx['value']} (order: {tx['order_amount']})")
        else:
            print("❌ No transactions found with non-zero commission in any obvious fields")
            
            # Check if all transactions are pending status
            statuses = [tx.get('status_id', 'unknown') for tx in transactions]
            unique_statuses = set(statuses)
            print(f"\n📊 Transaction statuses found: {unique_statuses}")
            
            # Check if there are confirmed transactions
            confirmed_count = sum(1 for s in statuses if s in [2, 'confirmed'])
            pending_count = sum(1 for s in statuses if s in [1, 'pending'])
            print(f"   • Confirmed transactions: {confirmed_count}")
            print(f"   • Pending transactions: {pending_count}")
            
            if confirmed_count == 0:
                print("\n💡 All transactions appear to be pending - this might explain zero commissions!")
                print("   Commissions are typically only set when transactions are confirmed.")
        
    except Exception as e:
        print(f"❌ Error debugging commission fields: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_commission_fields()
